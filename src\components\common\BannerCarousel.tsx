"use client";

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';
import { Autoplay, Pagination } from 'swiper/modules';
import { Banner } from '@/data/banners';


import 'swiper/css';
import 'swiper/css/pagination';
import { Swiper, SwiperSlide } from 'swiper/react';

interface BannerCarouselProps {
  banners: string[] | Banner[];
  interval?: number;
  className?: string;
  showInfo?: boolean;
}

export default function BannerCarousel({
  banners,
  interval = 10000,
  className = "",
}: BannerCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const getBannerData = (index: number) => {
    if (!banners || banners.length === 0) {
      return { image: '', title: '', description: '', link: '#' };
    }
    const banner = banners[index % banners.length];
    if (typeof banner === 'string') {
      return { image: banner, title: `Banner ${index + 1}`, description: '', link: '#' };
    }
    return banner;
  };

  const currentBanner = banners && banners.length > 0 ? getBannerData(currentIndex) : null;
  const nextBanner = banners && banners.length > 0 ? getBannerData(currentIndex + 1) : null;

  useEffect(() => {
    if (banners.length <= 1) return;

    const timer = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % banners.length);
        setIsTransitioning(false);
      }, 1000);
    }, interval);

    return () => clearInterval(timer);
  }, [banners.length, interval]);

  if (!banners || banners.length === 0) {
    return (
      <div className={`relative overflow-hidden ${className}`}>
        <div className="aspect-video w-full bg-gray-200 animate-pulse" />
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {currentBanner && nextBanner && (
        <div className="hidden xl:block">
          <Link href={typeof currentBanner === 'string' ? '#' : (currentBanner.link || '#')}>
            <div
              className={`absolute left-0 top-0 w-full h-1/2 transition-all duration-1000 ease-out cursor-pointer ${isTransitioning ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
                }`}
            >
              <Image
                src={currentBanner.image}
                alt={currentBanner.title}
                fill
                className="object-fill"
                priority={true}
                sizes="(max-width: 1280px) 100vw, 50vw"
              />
            </div>
          </Link>

          <Link href={typeof nextBanner === 'string' ? '#' : (nextBanner.link || '#')}>
            <div
              className={`absolute left-0 bottom-0 w-full h-1/2 transition-all duration-1000 ease-out cursor-pointer ${isTransitioning ? 'opacity-100 scale-100' : 'opacity-100 scale-100'
                }`}
            >
              <Image
                src={nextBanner.image}
                alt={nextBanner.title}
                fill
                className="object-fill"
                priority={false}
                sizes="(max-width: 1280px) 100vw, 50vw"
              />
            </div>
          </Link>
        </div>
      )}

      {/* Mobile & Tablet View - Simple Swiper */}
      {banners && banners.length > 0 && (
        <div className="xl:hidden">
          <Swiper
            grabCursor={true}
            centeredSlides={true}
            slidesPerView={1.3}
            spaceBetween={-30}
            loop={true}
            autoplay={{
              delay: interval,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            }}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            modules={[Autoplay, Pagination]}
            className="w-full"
            style={{
              '--swiper-pagination-color': '#ffffff',
              '--swiper-pagination-bullet-inactive-color': 'rgba(255, 255, 255, 0.5)',
            } as React.CSSProperties}
            onSlideChange={(swiper) => {
              swiper.slides.forEach((slide, index) => {
                slide.style.transition = 'transform 0.3s ease';
                if (index === swiper.activeIndex) {
                  slide.style.transform = 'scale(1)';
                  slide.style.opacity = '1';
                } else {
                  slide.style.transform = 'scale(0.8)';
                  slide.style.opacity = '0.6';
                }
              });
            }}
            onInit={(swiper) => {
              swiper.slides.forEach((slide, index) => {
                slide.style.transition = 'transform 0.3s ease';
                if (index === swiper.activeIndex) {
                  slide.style.transform = 'scale(1)';
                  slide.style.opacity = '1';
                } else {
                  slide.style.transform = 'scale(0.8)';
                  slide.style.opacity = '0.6';
                }
              });
            }}
          >
            {banners.map((banner, index) => {
              const bannerData = typeof banner === 'string' ? { image: banner, title: `Banner ${index + 1}`, description: '', link: '#' } : banner;
              return (
                <SwiperSlide key={index}>
                  <Link href={bannerData.link || '#'}>
                    <div className="relative w-full aspect-[16/9] rounded-lg overflow-hidden shadow-lg cursor-pointer">
                      <Image
                        src={bannerData.image}
                        alt={bannerData.title}
                        fill
                        className="object-cover"
                        priority={index === 0}
                        sizes="(max-width: 768px) 80vw, (max-width: 1024px) 60vw, 40vw"
                      />
                    </div>
                  </Link>
                </SwiperSlide>
              );
            })}
          </Swiper>
        </div>
      )}
    </div>
  );
}
