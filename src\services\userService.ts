export interface UserInfo {
  uid: string;
  displayName: string;
  email?: string;
  photoURL?: string;
  [key: string]: unknown;
}

// Function để lấy tất cả users (trả về array)
export const getAllUsers = async (role: string, limit: number): Promise<UserInfo[] | null> => {
  const url = `/api/proxy/forum/search-users?&role=${role}&limit=${limit}`;
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const userData = await response.json();
    console.log("All users data:", userData);

    // Response có thể là array trực tiếp hoặc có property users
    if (Array.isArray(userData)) {
      return userData;
    }
    return userData.users || userData;
  } catch (error) {
    console.error('Failed to get all users:', error);
    return null;
  }
};

// Function để tìm user cụ thể theo BLV ID
export const getUserByBLVId = async (blvId: string): Promise<UserInfo | null> => {
  try {
    // L<PERSON>y tất cả admin users
    const allUsers = await getAllUsers("admin", 1000);

    if (!allUsers || !Array.isArray(allUsers)) {
      return null;
    }

    console.log("Looking for BLV ID:", blvId);
    console.log("All users:", allUsers);

    // Tìm user có uid khớp với blvId
    const foundUser = allUsers.find((user: any) => user.uid === blvId);

    if (foundUser) {
      console.log("Found BLV user:", foundUser);
      return foundUser;
    }

    // Nếu không tìm thấy, trả về user đầu tiên làm fallback
    console.log("BLV not found, using first user as fallback");
    return allUsers[0] || null;

  } catch (error) {
    console.error('Failed to get user by BLV ID:', error);
    return null;
  }
};

// Backward compatibility - giữ function cũ
export const getUserInfo = getUserByBLVId;
