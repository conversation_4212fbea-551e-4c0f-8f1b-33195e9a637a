export interface UserInfo {
  uid: string;
  displayName: string;
  email?: string;
  photoURL?: string;
  [key: string]: unknown;
}

export const getUserInfo = async (role:string, limit:number): Promise<UserInfo | null> => {
  const url = `/api/proxy/forum/search-users?&role=${role}&limit=${limit}`;
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const userData = await response.json();
    console.log("User data:", userData);


    return userData.users;
  } catch (error) {
    console.error('Failed to get user info:', error);
    return null;
  }
};
