export interface UserInfo {
  uid: string;
  displayName: string;
  email?: string;
  photoURL?: string;
  [key: string]: unknown;
}

export const getAllUsers = async (role: string, limit: number): Promise<UserInfo[] | null> => {
  const url = `/api/proxy/forum/search-users?&role=${role}&limit=${limit}`;
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const userData = await response.json();

    if (Array.isArray(userData)) {
      return userData;
    }
    return userData.users || userData;
  } catch (error) {
    console.error('Failed to get all users:', error);
    return null;
  }
};

export const getUserByBLVId = async (blvId: string): Promise<UserInfo | null> => {
  try {
    const endpoints = [
      `/api/proxy/forum/search-users?id=${blvId}`
    ];  

    for (const url of endpoints) {
      try {
        const response = await fetch(url);

        if (response.ok) {
          const userData = await response.json();
          if (userData && userData.uid === blvId) {
            return userData;
          }

          if (Array.isArray(userData)) {
            const found = userData.find((user: any) => user.uid === blvId);
            if (found) {
              return found;
            }
          }
        }
      } catch (err) {
        console.log("Endpoint failed:", url, err);
      }
    }
    return await findUserInRoleLists(blvId);

  } catch (error) {
    console.error('Failed to get user by direct API:', error);
    return await findUserInRoleLists(blvId);
  }
};

const findUserInRoleLists = async (blvId: string): Promise<UserInfo | null> => {
  const roles = ["admin", "commentator"];

  for (const role of roles) {
    try {
      const allUsers = await getAllUsers(role, 1000);

      if (!allUsers || !Array.isArray(allUsers)) {
        continue;
      }
      const foundUser = allUsers.find((user: any) => user.uid === blvId);

      if (foundUser) {
        return foundUser;
      }
      // if (role === "admin") {
      //   console.log("Available UIDs in admin:", allUsers.map((user: any) => user.uid));
      // }

    } catch (error) {
      console.error(`Failed to search in ${role} list:`, error);
    }
  }
  try {
    const adminUsers = await getAllUsers("admin", 1000);
    return (adminUsers && adminUsers[0]) || null;
  } catch (error) {
    console.error('Failed to get fallback user:', error);
    return null;
  }
};

export const getUserInfo = getUserByBLVId;
