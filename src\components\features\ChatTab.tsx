"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import { 
  ChatMessage as ChatMessageType, 
  subscribeToChat, 
  sendChatMessage, 
  getRandomColor,
  fetchOlderMessages,
  getChatUserCount,
  updateUserPresence
} from '@/services/chatService';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';

interface ChatTabProps {
  isLoggedIn: boolean;
  onOpenAuthModal: (mode: 'login' | 'register') => void;
  matchId?: string;
}

export default function ChatTab({ isLoggedIn, onOpenAuthModal, matchId = 'default' }: ChatTabProps) {
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [totalMessageCount, setTotalMessageCount] = useState(0);
  const [userCount, setUserCount] = useState(0);
  const [canLoadMore, setCanLoadMore] = useState(true);
  const [replyTo, setReplyTo] = useState<ChatMessageType | null>(null);
  
  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Get user data from localStorage
  const getUserData = useCallback(() => {
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('userData');
      if (storedUser) {
        try {
          return JSON.parse(storedUser);
        } catch (error) {
          console.error('Error parsing user data:', error);
        }
      }
    }
    return null;
  }, []);

  const scrollToBottom = useCallback(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }
  }, []);

  const loadOlderMessages = useCallback(async () => {
    if (isLoadingMore || messages.length === 0) return;
    
    setIsLoadingMore(true);
    const oldestMessage = messages[0];
    
    try {
      const olderMessages = await fetchOlderMessages(matchId, oldestMessage.timestamp, 20);
      if (olderMessages.length > 0) {
        setMessages(prev => [...olderMessages, ...prev]);
        if (olderMessages.length < 20) {
          setCanLoadMore(false);
        }
      } else {
        setCanLoadMore(false);
      }
    } catch (error) {
      console.error('Error loading older messages:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, messages, matchId]);

  useEffect(() => {
    if (!matchId) return;

    const unsubscribe = subscribeToChat(
      matchId,
      (newMessages, totalCount) => {
        setMessages(newMessages);
        if (totalCount !== undefined) {
          setTotalMessageCount(totalCount);
        }
        setIsLoading(false);
      },
      50
    );

    unsubscribeRef.current = unsubscribe;

    // Get user count
    getChatUserCount(matchId).then(setUserCount);

    // Update user presence
    const userData = getUserData();
    if (userData?.userId) {
      updateUserPresence(matchId, userData.userId, true);
    }

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
      // Update user presence to offline
      const userData = getUserData();
      if (userData?.userId) {
        updateUserPresence(matchId, userData.userId, false);
      }
    };
  }, [matchId, getUserData]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      const timer = setTimeout(() => {
        scrollToBottom();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [messages.length, scrollToBottom]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const isScrolledUp = scrollTop < scrollHeight - clientHeight - 100;
      setShowScrollToBottom(isScrolledUp);
      
      // Check if we can load more messages
      if (scrollTop < 100 && canLoadMore && !isLoadingMore && messages.length > 0) {
        loadOlderMessages();
      }
    }
  }, [canLoadMore, isLoadingMore, messages.length, loadOlderMessages]);

  const handleSubmit = useCallback(async (messageText: string, replyToMessage?: ChatMessageType) => {
    if (!messageText.trim() || !isLoggedIn) return;

    const userData = getUserData();
    if (!userData) {
      onOpenAuthModal('login');
      return;
    }

    try {
      const success = await sendChatMessage(
        matchId,
        userData.userId || 'anonymous',
        userData.name || 'Anonymous',
        userData.name?.[0]?.toUpperCase() || 'A',
        getRandomColor(),
        messageText,
        userData.verified || false,
        replyToMessage ? {
          id: replyToMessage.id,
          userName: replyToMessage.userName,
          message: replyToMessage.message,
          userId: replyToMessage.userId
        } : undefined
      );

      if (!success) {
        console.error('Failed to send message');
        // Optionally show error message to user
      } else {
        // Clear reply state after successful send
        setReplyTo(null);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }, [isLoggedIn, matchId, getUserData, onOpenAuthModal]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // This will be handled by ChatInput component
    }
  }, []);

  const renderMessage = useCallback((msg: ChatMessageType) => {
    const userData = getUserData();
    const isOwnMessage = userData?.userId === msg.userId;
    
    return (
      <ChatMessage
        key={msg.id}
        message={msg}
        isOwnMessage={isOwnMessage}
        onReply={(replyMessage) => {
          setReplyTo(replyMessage);
        }}
        onReact={(messageId, reactionType) => {
          // Handle reaction - you can implement this later
        }}
        onPin={(messageId, pinned) => {
          // Handle pin - you can implement this later
        }}
        onDelete={(messageId) => {
          // Handle delete - you can implement this later
        }}
        isAdmin={userData?.isAdmin || false}
      />
    );
  }, [getUserData]);

  return (
    <div className="flex flex-col h-full relative" style={{ 
      paddingBottom: 'env(safe-area-inset-bottom, 0px)'
    }}>
      {/* Chat Messages */}
      <div
        className="flex-1 overflow-y-auto p-4 relative"
        ref={chatContainerRef}
        onScroll={handleScroll}
        style={{
          paddingBottom: '20px'
        }}
      >
        {/* Loading indicator for older messages */}
        {isLoadingMore && (
          <div className="text-center py-2">
            <div className="inline-flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              <span>Đang tải tin nhắn cũ...</span>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className="space-y-2">
          {messages.map(renderMessage)}
        </div>

        {/* Login to comment card */}
        {!isLoggedIn && (
          <div
            className="mt-4 p-4 bg-white dark:bg-custom-dark border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm cursor-pointer relative z-20 hover:bg-white dark:hover:bg-gray-700 transition-colors"
            onClick={() => onOpenAuthModal('login')}
          >
            <div className="text-center">
              <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">Đăng nhập để bình luận</p>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <ChatInput
        onSubmit={handleSubmit}
        isLoggedIn={isLoggedIn}
        replyTo={replyTo}
        onCancelReply={() => setReplyTo(null)}
        placeholder="Nhập tin nhắn..."
        disabled={false}
      />

      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <div className="absolute bottom-[70px] right-4 z-20">
          <button
            onClick={scrollToBottom}
            className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white shadow-lg hover:bg-blue-600 transition-all duration-200 hover:scale-105"
            title="Scroll xuống tin nhắn mới nhất"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
} 