import { ApiResponse, FetchMatchesParams, MatchData } from '@/types/match';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.ngoaihangtv.xyz';

interface ApiResponseItem {
  id?: string;
  league?: string;
  category?: string;
  homeTeam?: {
    logo?: string;
    name?: string;
    score?: number;
  };
  awayTeam?: {
    logo?: string;
    name?: string;
    score?: number;
  };
  cards?: {
    redAway?: number;
    redHome?: number;
    yellowAway?: number;
    yellowHome?: number;
  };
  odds?: {
    asian?: {
      away: string;
      home: string;
      handicap: string;
    };
    european?: {
      away: string;
      draw: string;
      home: string;
    };
    overUnder?: {
      line: string;
      over: string;
      under: string;
    };
  } | null;
  liveData?: {
    time?: string;
    event?: string;
    description?: string;
    [key: string]: unknown;
  }[];
  liveTrack?: string | null;
  typeMatch?: string;
  status?: string;
  date?: string;
  time?: string;
  links?: string[];
  viewFake?: number;
  liveFake?: number;
  createdAt?: string;
  updatedAt?: string;
  parseData?: {
    time?: string;
    status?: string;
  } | null;
  title?: string | null;
  imageUrl?: string | null;
  author?: string | null;
  hashtags?: string | null;
  timestamp?: string | null;
  flv?: {
    url?: string;
    quality?: string;
    [key: string]: unknown;
  } | null;
  _ownLeague?: boolean;
  _ownHomeTeam?: boolean;
  _ownAwayTeam?: boolean;
  _ownCards?: boolean;
  _ownOdds?: boolean;
  _ownLiveTrack?: boolean;
  _ownStatus?: boolean;
  _ownDate?: boolean;
  _ownTime?: boolean;
  _ownParseData?: boolean;
  key_sync?: string | null;
  incidents?: {
    cards?: {
      red?: number;
      yellow?: number;
    };
    goals?: {
      home?: number;
      away?: number;
    };
    [key: string]: unknown;
  } | null;
  statistics?: {
    possession?: {
      home?: number;
      away?: number;
    };
    shots?: {
      home?: number;
      away?: number;
    };
    [key: string]: unknown;
  } | null;
  [key: string]: unknown;
}

const mapApiResponseToMatchData = (apiData: ApiResponseItem[]): MatchData[] => {
  return apiData
    .map((item: ApiResponseItem) => ({
      id: item.id || '',
      league: item.league || '',
      category: item.category || '',
      homeTeam: {
        logo: item.homeTeam?.logo || '',
        name: item.homeTeam?.name || '',
        score: item.homeTeam?.score || 0,
      },
      awayTeam: {
        logo: item.awayTeam?.logo || '',
        name: item.awayTeam?.name || '',
        score: item.awayTeam?.score || 0,
      },
      cards: {
        redAway: item.cards?.redAway || 0,
        redHome: item.cards?.redHome || 0,
        yellowAway: item.cards?.yellowAway || 0,
        yellowHome: item.cards?.yellowHome || 0,
      },
      odds: item.odds || null,
      liveData: item.liveData || [],
      liveTrack: item.liveTrack || null,
      typeMatch: item.typeMatch || '',
      status: item.status || '',
      date: item.date || '',
      time: item.time || '',
      links: item.links || [],
      viewFake: item.viewFake || 0,
      liveFake: item.liveFake || 0,
      createdAt: item.createdAt || '',
      updatedAt: item.updatedAt || '',
      parseData: item.parseData || null,
      title: item.title || null,
      imageUrl: item.imageUrl || null,
      author: item.author || null,
      hashtags: item.hashtags || null,
      timestamp: item.timestamp || null,
      flv: item.flv || null,
      _ownLeague: item._ownLeague || false,
      _ownHomeTeam: item._ownHomeTeam || false,
      _ownAwayTeam: item._ownAwayTeam || false,
      _ownCards: item._ownCards || false,
      _ownOdds: item._ownOdds || false,
      _ownLiveTrack: item._ownLiveTrack || false,
      _ownStatus: item._ownStatus || false,
      _ownDate: item._ownDate || false,
      _ownTime: item._ownTime || false,
      _ownParseData: item._ownParseData || false,
      key_sync: item.key_sync || null,
      incidents: item.incidents || null,
      statistics: item.statistics || null,
      href: '#',
    }))
    .filter((match) => {
      // Filter out matches with missing essential data
      const hasValidId = match.id && match.id.trim() !== '';
      const hasValidStatus = match.status && match.status.trim() !== '';
      const hasValidHomeTeam = match.homeTeam && 
        match.homeTeam.name && match.homeTeam.name.trim() !== '' &&
        typeof match.homeTeam.score === 'number';
      const hasValidAwayTeam = match.awayTeam && 
        match.awayTeam.name && match.awayTeam.name.trim() !== '' &&
        typeof match.awayTeam.score === 'number';
      
      return hasValidId && hasValidStatus && hasValidHomeTeam && hasValidAwayTeam;
    });
};

export const fetchMatches = async (params: FetchMatchesParams = {}): Promise<{ data: MatchData[], total: number }> => {
  const {
    category = '',
    status = '',
    typeMatch = '',
    limit = 9,
    offset = 0,
    sortBy = 'status,date',
    sortOrder = 'DESC,ASC',
    date = ''
  } = params;

  const queryParams = new URLSearchParams({
    category,
    limit: limit.toString(),
    offset: offset.toString(),
    sortBy,
    sortOrder
  });

  if (status) {
    queryParams.append('status', status);
  }

  if (typeMatch) {
    queryParams.append('typeMatch', typeMatch);
  }

  if (date) {
    queryParams.append('date', date);
  }

  const url = `${API_BASE_URL}/proxy/match/filter?${queryParams.toString()}`;
  
  // console.log('🌐 API URL:', url);
  // console.log('📋 Query Params:', Object.fromEntries(queryParams.entries()));

  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData: ApiResponse = await response.json();
    
    const mappedData = mapApiResponseToMatchData(responseData.data as ApiResponseItem[]);
    return {
      data: mappedData,
      total: responseData.total || mappedData.length
    };
  } catch (error) {
    console.error('Failed to fetch matches:', error);
    // Return empty data instead of throwing to prevent app crashes
    return {
      data: [],
      total: 0
    };
  }
};


export const fetchAllMatches = async (category: string = '') => {
  const result = await fetchMatches({ 
    category, 
    limit: 20,
    offset: 0,
    sortBy: 'status,time,date',
    sortOrder: 'DESC,ASC,ASC'
  });
  return result;
};

export const getAllCount = async (category: string = '') => {
  const url = `${API_BASE_URL}/proxy/match/counter-by-category?category=${category || 'football'}`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();

    return responseData;
  } catch (error) {
    console.error('Failed to get all count:', error);
    // Return default counts instead of throwing
    return {
      all: 0,
      live: 0,
      hot: 0,
      today: 0,
      tomorrow: 0
    };
  }
};

export const fetchMatchDetails = async (matchId: string): Promise<MatchData | null> => {
  const url = `${API_BASE_URL}/proxy/match/detail/${matchId}`;
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.match;
  } catch (error) {
    console.error('Failed to fetch match details:', error);
    return null;
  }
};

export const fetchStatistics = async (
  category: string,
  typeMatch: string,
  limit: number,
  offset: number,
  sortBy: string[] = ["status", "time", "date"],
  sortOrder: string[] = ["DESC", "ASC", "ASC"]
): Promise<{ data: MatchData[]; total: number } | null> => {
  // build query string
  const url = `${API_BASE_URL}/proxy/match/filter?` +
    `category=${category}&` +
    `typeMatch=${typeMatch}&` +
    `limit=${limit}&` +
    `offset=${offset}&` +
    `sortBy=${sortBy.join(",")}&` +
    `sortOrder=${sortOrder.join(",")}`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();

    // Map response tương tự như fetchMatches
    const mappedData = mapApiResponseToMatchData(responseData.data as ApiResponseItem[]);
    return {
      data: mappedData,
      total: responseData.total || mappedData.length,
    };
  } catch (error) {
    console.error("Failed to fetch statistics:", error);
    return null;
  }
};
