@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --container-max-width: 1920px;
    --container-padding-x: 1.5rem;
    --container-padding-y: 2rem;
  }

  .dark {
    --background: 0 0% 3.5%; /* #090909 */
    --foreground: 0 0% 98%;
    --card: 0 0% 3.5%; /* #090909 */
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.5%; /* #090909 */
    --popover-foreground: 0 0% 98%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 0 0% 10%; /* #1a1a1a */
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 10%; /* #1a1a1a */
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 10%; /* #1a1a1a */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 0 0% 10%; /* #1a1a1a */
    --input: 0 0% 10%; /* #1a1a1a */
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .container-wide {
    max-width: var(--container-max-width);
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--container-padding-x);
    padding-right: var(--container-padding-x);
  }

  .container-content {
    max-width: 1600px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 10px;
    padding-right: 10px;
  }

  .container-wide-y {
    padding-top: var(--container-padding-y);
    padding-bottom: var(--container-padding-y);
  }

  .container-wide-px {
    padding-left: var(--container-padding-x);
    padding-right: var(--container-padding-x);
  }

  .container-wide-py {
    padding-top: var(--container-padding-y);
    padding-bottom: var(--container-padding-y);
  }

  /* Container without padding for sections that need custom spacing */
  .container-wide-no-padding {
    max-width: var(--container-max-width);
    margin-left: auto;
    margin-right: auto;
  }

  /* Video break-out container */
  .video-breakout {
    margin-left: calc(-1 * var(--container-padding-x));
    margin-right: calc(-1 * var(--container-padding-x));
    width: calc(100% + 2 * var(--container-padding-x));
    max-width: 100vw;
  }

  /* Full-width video container */
  .video-full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    max-width: none;
  }

  /* Video container that ignores parent constraints */
  .video-ignore-container {
    position: relative;
    width: 100vw;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    max-width: none;
  }

  /* Container break-out for specific sections */
  .break-out-container {
    @media (max-width: 1023px) {
      margin-left: calc(-1 * var(--container-padding-x));
      margin-right: calc(-1 * var(--container-padding-x));
      width: calc(100% + 2 * var(--container-padding-x));
      max-width: 100vw;
    }

    @media (min-width: 1024px) {
      width: 100%;
      max-width: none;
      margin: 0;
    }
  }

  /* Video and banner break-out with proper padding calculation */
  .video-mobile-breakout,
  .banner-mobile-breakout {
    @media (max-width: 1023px) {
      /* position: relative;
    width: 100vw;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    max-width: none; */
    }

    @media (min-width: 1024px) {
      width: 100%;
      max-width: none;
      margin: 0;
      left: auto;
      right: auto;
    }
  }

  /* Full width layout override */
  .full-width-layout {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    max-width: none;
    overflow-x: hidden;
  }

  /* Container break-out for specific sections */
  .break-out-container {
    margin-left: calc(-1 * var(--container-padding-x));
    margin-right: calc(-1 * var(--container-padding-x));
    width: calc(100% + 2 * var(--container-padding-x));
    max-width: 100vw;
  }
}

@layer utilities {
  /* Custom dark theme colors */
  .dark\:bg-custom-dark {
    background-color: #090909;
  }

  .dark\:border-custom-dark {
    border-color: #1a1a1a;
  }

  .dark\:text-custom-light {
    color: #ffffff;
  }

  .dark\:text-custom-muted {
    color: #cccccc;
  }

  .dark\:text-custom-subtle {
    color: #999999;
  }

  .dark\:bg-custom-secondary {
    background-color: #1a1a1a;
  }

  .dark\:bg-custom-muted {
    background-color: #0f0f0f;
  }

  /* Enhanced match card styling */
  .match-card-enhanced {
    @apply border-2;
    box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.15), 2px 2px 4px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(0, 0, 0, 0.05);
  }

  .dark .match-card-enhanced {
    box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.4), 2px 2px 4px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1), 0 0 0 2px rgba(0, 0, 0, 0.2);
  }
}

/* Custom scrollbar */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Scrollbar hide utility */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Smooth horizontal scroll */
.scroll-smooth-x {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Banner animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

/* Match card fade-in animation */
@keyframes match-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-match-fade-in {
  animation: match-fade-in 0.6s ease-out forwards;
  /* Ensure animation doesn't affect layout */
  will-change: opacity, transform;
  /* Preserve all existing styles */
  display: block;
}

/* Marquee animation for top banner */
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee {
  animation: marquee 20s linear infinite;
}

/* Loading Screen animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Loading screen fade transitions */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.animate-fade-out {
  animation: fade-out 0.5s ease-out forwards;
}

/* Skeleton loading animations */
@keyframes skeleton-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-skeleton-pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

/* Enhanced skeleton loading with shimmer effect */
@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-shimmer 1.5s infinite;
}

/* Dark mode shimmer */
.dark .skeleton-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
  animation: skeleton-shimmer 1.5s infinite;
}
