import Link from "next/link";
import Image from "next/image";
import { getBannerBySlug } from "@/data/banners";
import { notFound } from "next/navigation";

export default async function PromoDetail({ params }: { params: Promise<{ slug: string }> }) {
  const resolvedParams = await params;
  const banner = getBannerBySlug(resolvedParams.slug);

  if (!banner) {
    notFound();
  }

  const getBadgeColor = (id: number) => {
    switch (id) {
      case 1: return "bg-red-500"; // HOT
      case 2: return "bg-green-500"; // NEW
      case 3: return "bg-blue-500"; // REFERRAL
      case 4: return "bg-purple-500"; // DAILY
      case 5: return "bg-orange-500"; // NEWBIE
      case 6: return "bg-yellow-500"; // EVENT
      case 7: return "bg-pink-500"; // CASINO
      default: return "bg-gray-500";
    }
  };

  const getBadgeText = (id: number) => {
    switch (id) {
      case 1: return "HOT";
      case 2: return "NEW";
      case 3: return "REF";
      case 4: return "DAILY";
      case 5: return "NEWBIE";
      case 6: return "EVENT";
      case 7: return "CASINO";
      default: return "PROMO";
    }
  };

  return (
    <main className="min-h-screen bg-white dark:bg-custom-dark text-zinc-900 dark:text-white">
      <section className="border-b border-zinc-200 dark:border-gray-700 bg-white dark:bg-custom-dark">
        <div className="mx-auto max-w-7xl px-4 py-8">
          <div className="mb-4 flex items-center gap-2 text-sm text-zinc-600 dark:text-gray-400">
            <Link href="/khuyen-mai" className="hover:underline">Khuyến mãi</Link>
            <span>/</span>
            <span>{banner.title}</span>
          </div>
          <h1 className="text-2xl font-bold">{banner.title}</h1>
        </div>
      </section>

      <section className="mx-auto max-w-7xl px-2 py-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-12">
          <div className="lg:col-span-8">
            <div className="relative mb-4 aspect-[16/9] overflow-hidden rounded-xl border border-zinc-200 dark:border-gray-700 bg-zinc-50 dark:bg-gray-700">
              <Image src={banner.image} alt={banner.title} fill className="object-fill" />
            </div>
            <div className="rounded-xl border border-zinc-200 dark:border-gray-700 bg-white dark:bg-custom-dark p-4 text-sm text-zinc-700 dark:text-gray-300">
              <div className="whitespace-pre-line leading-relaxed">
                {banner.content}
              </div>
            </div>
          </div>
          <aside className="lg:col-span-4">
            <div className="sticky top-20 space-y-4">
              <div className="rounded-xl border border-zinc-200 dark:border-gray-700 bg-white dark:bg-custom-dark p-4">
                <div className="mb-2 text-sm font-semibold text-zinc-900 dark:text-white">Thông tin</div>
                <div className="flex items-center gap-2 text-sm">
                  <span className={`rounded ${getBadgeColor(banner.id)} px-2 py-1 text-[11px] font-semibold text-white`}>
                    {getBadgeText(banner.id)}
                  </span>
                  <span className="text-zinc-700 dark:text-gray-300">Ưu đãi đang diễn ra</span>
                </div>
                <div className="mt-3 text-xs text-zinc-600 dark:text-gray-400">
                  {banner.description}
                </div>
              </div>
              <div className="rounded-xl border border-zinc-200 dark:border-gray-700 bg-white dark:bg-custom-dark p-4">
                <div className="mb-2 text-sm font-semibold text-zinc-900 dark:text-white">Tham gia ngay</div>
                <button className="w-full rounded-lg bg-blue-600 px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700 transition-colors">
                  Nhận ưu đãi
                </button>
              </div>
            </div>
          </aside>
        </div>
      </section>
    </main>
  );
}
