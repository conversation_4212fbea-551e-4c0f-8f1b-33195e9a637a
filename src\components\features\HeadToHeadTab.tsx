"use client";

interface HeadToHeadTabProps {
  isLoading?: boolean;
}

export default function HeadToHeadTab({ isLoading = false }: HeadToHeadTabProps) {
  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="h-full space-y-2 p-2">
      {/* Overall Record Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Recent Matches Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Goals Scored Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Notable Matches Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Season Performance Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="text-center py-3">
          <div className="space-y-2">
            <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );

  // Show skeleton when loading
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="h-full space-y-2 p-2">
      {/* Overall Record */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Thống kê đối đầu</h3>
        <div className="text-center py-3">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
      </div>

      {/* Recent Matches */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">5 trận gần nhất</h3>
        <div className="text-center py-3">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
      </div>

      {/* Goals Scored */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Bàn thắng ghi được</h3>
        <div className="text-center py-3">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
      </div>

      {/* Notable Matches */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Những trận đấu đáng nhớ</h3>
        <div className="text-center py-3">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
      </div>

      {/* Season Performance */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Thành tích mùa giải</h3>
        <div className="text-center py-3">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
      </div>
    </div>
  );
} 