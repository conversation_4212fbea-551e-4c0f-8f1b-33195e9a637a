"use client";

import Image from "next/image";

export default function MatchStatus() {
  return (
    <div className="flex md:justify-evenly items-center dark:bg-white justify-center gap-2 [&_div]:px-1 my-4 lg:mb-3 relative z-10 border border-gray-700 border-opacity-25 rounded-3xl px-2 py-1 lg:mx-[100px] sm:mx-[80px] mx-16 dark:divide-x-2 divide-yellow-600 divide-x-2">
      <div className="flex gap-2">
        <p className="p-1 text-green-500 text-xs font-semibold">HT</p>
        <span className="dark:text-black">0 - 0</span>
      </div>
      <div className="flex gap-2">
        <Image src="/icon/corner.svg" alt="corner" width={18} height={18} />
        <span className="dark:text-black">0 - 0</span>
      </div>
      <div className="flex gap-2">
        <Image src="/icon/yellow.svg" alt="yellow" width={18} height={18} />
        <span className="dark:text-black">0 - 0</span>
      </div>
    </div>
  );
}
