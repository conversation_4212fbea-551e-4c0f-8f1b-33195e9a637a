"use client";

import { MatchData, StatisticsData } from "@/types/match";
import { useEffect, useState } from "react";

import Image from "next/image";
import { fetchMatchStatistics } from "@/services/matchService";

export default function MatchStatus({ match }: { match: MatchData }) {
  const [statistics, setStatistics] = useState<StatisticsData | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);
  useEffect(() => {
    const fetchStats = async () => {
      if (!match.id) return;

      setStatsLoading(true);
      try {
        const statsData = await fetchMatchStatistics(match.id);

        console.log("Statistics response:", statsData);

        if (statsData && Array.isArray(statsData) && statsData.length > 0) {
          const fullMatchStats =
            statsData.find((stat) => stat.type === 0) || statsData[0];

          if (
            fullMatchStats &&
            fullMatchStats.stats &&
            fullMatchStats.stats.length >= 2
          ) {
            console.log("Found match statistics:", fullMatchStats);

            // Convert sang format StatisticsData
            const homeStats = fullMatchStats.stats[0];
            const awayStats = fullMatchStats.stats[1];

            const convertedStats: StatisticsData = {
              possession: {
                home: homeStats.ball_possession || 0,
                away: awayStats.ball_possession || 0,
              },
              shots: {
                home: homeStats.shots || 0,
                away: awayStats.shots || 0,
              },
              shotsOnTarget: {
                home: homeStats.shots_on_target || 0,
                away: awayStats.shots_on_target || 0,
              },
              corners: {
                home: homeStats.corner_kicks || 0,
                away: awayStats.corner_kicks || 0,
              },
              yellowCards: {
                home: homeStats.yellow_cards || 0,
                away: awayStats.yellow_cards || 0,
              },
              redCards: {
                home: homeStats.red_cards || 0,
                away: awayStats.red_cards || 0,
              },
              attacks: {
                home: homeStats.attacks || 0,
                away: awayStats.attacks || 0,
              },
              dangerousAttacks: {
                home: homeStats.dangerous_attack || 0,
                away: awayStats.dangerous_attack || 0,
              },
            };

            console.log("Converted statistics:", convertedStats);
            setStatistics(convertedStats);
          } else {
            console.log(
              "Invalid statistics structure, using match data:",
              match.statistics
            );
            setStatistics(match.statistics);
          }
        } else {
          console.log(
            "No statistics from API, using match data:",
            match.statistics
          );
          setStatistics(match.statistics);
        }
      } catch (error) {
        console.error("Failed to fetch statistics:", error);
        // Fallback về statistics có sẵn trong match
        setStatistics(match.statistics);
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStats();
  }, [match.id, match.statistics]);
  return (
    <div className="flex md:justify-evenly items-center dark:bg-white justify-center gap-2 [&_div]:px-1 my-4 lg:mb-3 relative z-10 border border-gray-700 border-opacity-25 rounded-3xl px-2 py-1 lg:mx-[100px] sm:mx-[80px] mx-16 dark:divide-x-2 divide-yellow-600 divide-x-2">
      <div className="flex gap-2">
        <p className="p-1 text-green-500 text-xs font-semibold">HT</p>
        <span className="dark:text-black">0 - 0</span>
      </div>
      <div className="flex gap-2">
        <Image src="/icon/corner.svg" alt="corner" width={18} height={18} />
        <span className="dark:text-black">0 - 0</span>
      </div>
      <div className="flex gap-2">
        <Image src="/icon/yellow.svg" alt="yellow" width={18} height={18} />
        <span className="dark:text-black">0 - 0</span>
      </div>
    </div>
  );
}
