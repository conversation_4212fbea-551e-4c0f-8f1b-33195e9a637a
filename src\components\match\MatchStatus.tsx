"use client";

import { MatchData, StatisticsData } from "@/types/match";
import { useEffect, useState } from "react";

import Image from "next/image";
import { fetchMatchStatistics } from "@/services/matchService";

export default function MatchStatus({ match }: { match: MatchData }) {
  const [statistics, setStatistics] = useState<StatisticsData | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);
  useEffect(() => {
    const fetchStats = async () => {
      if (!match.id) return;

      setStatsLoading(true);
      try {
        const statsData = await fetchMatchStatistics(match.id);

        console.log("Statistics response:", statsData);

        if (statsData && Array.isArray(statsData) && statsData.length > 0) {
          const fullMatchStats =
            statsData.find((stat) => stat.type === 0) || statsData[0];

          if (
            fullMatchStats &&
            fullMatchStats.stats &&
            fullMatchStats.stats.length >= 2
          ) {
            console.log("Found match statistics:", fullMatchStats);
            const homeStats = fullMatchStats.stats[0];
            const awayStats = fullMatchStats.stats[1];

            const convertedStats: StatisticsData = {
              possession: {
                home: homeStats.ball_possession || 0,
                away: awayStats.ball_possession || 0,
              },
              shots: {
                home: homeStats.shots || 0,
                away: awayStats.shots || 0,
              },
              shotsOnTarget: {
                home: homeStats.shots_on_target || 0,
                away: awayStats.shots_on_target || 0,
              },
              corners: {
                home: homeStats.corner_kicks || 0,
                away: awayStats.corner_kicks || 0,
              },
              yellowCards: {
                home: homeStats.yellow_cards || 0,
                away: awayStats.yellow_cards || 0,
              },
              redCards: {
                home: homeStats.red_cards || 0,
                away: awayStats.red_cards || 0,
              },
              attacks: {
                home: homeStats.attacks || 0,
                away: awayStats.attacks || 0,
              },
              dangerousAttacks: {
                home: homeStats.dangerous_attack || 0,
                away: awayStats.dangerous_attack || 0,
              },
            };
            setStatistics(convertedStats);
          } else {
            console.log(
              "Invalid statistics structure, using match data:",
              match.statistics
            );
            setStatistics(match.statistics);
          }
        }
      } catch (error) {
        console.error("Failed to fetch statistics:", error);
        // Fallback về statistics có sẵn trong match
        setStatistics(match.statistics);
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStats();
  }, [match.id, match.statistics]);

  // Lấy tỷ số hiệp 1 - có thể từ statistics type 1 hoặc từ match data
  const getHalfTimeScore = () => {
    if (statistics?.goals) {
      return {
        home: statistics.goals.home || 0,
        away: statistics.goals.away || 0,
      };
    }

    // Fallback về tỷ số hiện tại
    return {
      home: match.homeTeam?.score || 0,
      away: match.awayTeam?.score || 0,
    };
  };

  const halfTimeScore = getHalfTimeScore();

  // Loading state
  if (statsLoading) {
    return (
      <div className="flex md:justify-evenly items-center dark:bg-white justify-center gap-2 [&_div]:px-1 my-4 lg:mb-3 relative z-10 border border-gray-700 border-opacity-25 rounded-3xl px-2 py-1 lg:mx-[100px] sm:mx-[80px] mx-16 dark:divide-x-2 divide-yellow-600 divide-x-2">
        <div className="flex gap-2">
          <p className="p-1 text-green-500 text-xs font-semibold">HT</p>
          <span className="dark:text-black">...</span>
        </div>
        <div className="flex gap-2">
          <Image
            src="/icon/corner.svg"
            alt="corner_kick"
            width={18}
            height={18}
          />
          <span className="dark:text-black">...</span>
        </div>
        <div className="flex gap-2">
          <Image
            src="/icon/yellow.svg"
            alt="yellow_card"
            width={18}
            height={18}
          />
          <span className="dark:text-black">...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex md:justify-evenly items-center dark:bg-white justify-center gap-2 [&_div]:px-1 my-4 lg:mb-3 relative z-10 border border-gray-700 border-opacity-25 rounded-3xl px-2 py-1 lg:mx-[100px] sm:mx-[80px] mx-16 dark:divide-x-2 divide-yellow-600 divide-x-2">
      {/* Half Time Score */}
      <div className="flex gap-2">
        <p className="p-1 text-green-500 text-xs font-semibold">HT</p>
        <span className="dark:text-black">
          {halfTimeScore.home} - {halfTimeScore.away}
        </span>
      </div>

      {/* Corner Kicks */}
      <div className="flex gap-2">
        <Image
          src="/icon/corner.svg"
          alt="corner_kick"
          width={18}
          height={18}
        />
        <span className="dark:text-black">
          {statistics?.corners?.home || 0} - {statistics?.corners?.away || 0}
        </span>
      </div>

      {/* Yellow Cards */}
      <div className="flex gap-2">
        <Image
          src="/icon/yellow.svg"
          alt="yellow_card"
          width={18}
          height={18}
        />
        <span className="dark:text-black">
          {statistics?.yellowCards?.home || 0} -{" "}
          {statistics?.yellowCards?.away || 0}
        </span>
      </div>
    </div>
  );
}
