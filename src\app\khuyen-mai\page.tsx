import Image from "next/image";
import Link from "next/link";
import { getActiveBanners } from "@/data/banners";

export default function PromotionsPage() {
  const banners = getActiveBanners();

  const getBadgeColor = (id: number) => {
    switch (id) {
      case 1: return "bg-red-500"; // HOT
      case 2: return "bg-green-500"; // NEW
      case 3: return "bg-blue-500"; // REFERRAL
      case 4: return "bg-purple-500"; // DAILY
      case 5: return "bg-orange-500"; // NEWBIE
      case 6: return "bg-yellow-500"; // EVENT
      case 7: return "bg-pink-500"; // CASINO
      default: return "bg-gray-500";
    }
  };

  const getBadgeText = (id: number) => {
    switch (id) {
      case 1: return "HOT";
      case 2: return "NEW";
      case 3: return "REF";
      case 4: return "DAILY";
      case 5: return "NEWBIE";
      case 6: return "EVENT";
      case 7: return "CASINO";
      default: return "PROMO";
    }
  };

  return (
    <>
      {/* Hero */}
      <section className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-custom-dark">
        <div className="py-4 sm:py-6 md:py-8">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Khuyến mãi nổi bật</h1>
          <p className="mt-2 max-w-2xl text-sm sm:text-base text-gray-600 dark:text-gray-300">Tổng hợp các chương trình ưu đãi hấp dẫn dành cho thành viên. Điều khoản áp dụng theo từng khuyến mãi.</p>
        </div>
      </section>

      {/* Promo cards */}
      <section className="py-4 sm:py-6">
        <div className="grid grid-cols-1 gap-3 sm:gap-4 sm:grid-cols-2 lg:grid-cols-2">
          {banners.map((banner) => (
            <div key={banner.id} className="group rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-custom-dark p-3 sm:p-4 shadow-sm transition hover:shadow-md">
              <div className="mb-2 sm:mb-3 flex items-center justify-between">
                <span className={`rounded ${getBadgeColor(banner.id)} px-1.5 sm:px-2 py-0.5 sm:py-1 text-[10px] sm:text-[11px] font-semibold text-white`}>
                  {getBadgeText(banner.id)}
                </span>
              </div>
              <Link href={`/khuyen-mai/${banner.slug}`} className="relative mb-2 sm:mb-3 block aspect-[16/9] overflow-hidden rounded-xl bg-white dark:bg-gray-700">
                <Image 
                  src={banner.image} 
                  alt={banner.title} 
                  fill 
                  className="object-contain p-4 sm:p-6 transition group-hover:scale-[1.02]" 
                />
              </Link>
              <Link href={`/khuyen-mai/${banner.slug}`} className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white hover:underline line-clamp-2">
                {banner.title}
              </Link>
              <p className="mt-1 text-xs sm:text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                {banner.description}
              </p>
              <div className="mt-2 sm:mt-3 flex gap-1.5 sm:gap-2">
                <Link 
                  href={`/khuyen-mai/${banner.slug}`} 
                  className="rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm font-medium text-gray-800 dark:text-gray-200 hover:bg-white dark:hover:bg-gray-600 transition-colors"
                >
                  Chi tiết
                </Link>
                <button className="rounded-lg bg-blue-600 px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm font-semibold text-white hover:bg-blue-700 transition-colors">
                  Nhận ưu đãi
                </button>
              </div>
            </div>
          ))}
        </div>
      </section>
    </>
  );
}
