import { useState, useEffect, useMemo } from 'react';
import { Banner, getActiveBanners, getBannersByPriority } from '@/data/banners';

interface UseBannersReturn {
  banners: Banner[];
  activeBanners: Banner[];
  featuredBanners: Banner[];
  loading: boolean;
  error: string | null;
  refreshBanners: () => void;
}

export const useBanners = (): UseBannersReturn => {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Memoized banner data
  const activeBanners = useMemo(() => getActiveBanners(), []);
  const featuredBanners = useMemo(() => getBannersByPriority(1), []);

  const refreshBanners = () => {
    setLoading(true);
    setError(null);
    
    try {
      setTimeout(() => {
        setBanners(getActiveBanners());
        setLoading(false);
      }, 100);
    } catch (err) {
      setError('Failed to load banners');
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshBanners();
  }, []);

  return {
    banners,
    activeBanners,
    featuredBanners,
    loading,
    error,
    refreshBanners,
  };
};
