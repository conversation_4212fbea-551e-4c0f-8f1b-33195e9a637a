"use client";

import { Too<PERSON><PERSON>, TooltipContent, TooltipTrigger } from "../ui/tooltip";
import { UserInfo, getUserInfo } from "@/services/userService";
import { formatTime, getStatusText } from "@/lib/utils";
import { useEffect, useRef, useState } from "react";

import Image from "next/image";
import { MatchData } from "@/types/match";
import MatchStatsPopup from "./MatchStatsPopup";
import MatchTooltip from "./MatchTooltip";

export type { MatchData };

interface MatchCardProps {
  match: MatchData;
  variant?: "default" | "compact" | "detailed";
  className?: string;
  onClick?: () => void;
  style?: React.CSSProperties;
}

export default function MatchCard({
  match,
  variant = "default",
  className = "",
  onClick,
  style,
}: MatchCardProps) {
  const [blvInfo, setBlvInfo] = useState<UserInfo | null>(null);
  const [blvLoading, setBlvLoading] = useState(false);
  const [showStatsPopup, setShowStatsPopup] = useState(false);
  const [popupPosition, setPopupPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [isHoveringPopup, setIsHoveringPopup] = useState(false);
  const [isHoveringStatus, setIsHoveringStatus] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Detect mobile device
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768 || "ontouchstart" in window);
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);

    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Handle click outside to close popup on mobile
  useEffect(() => {
    if (!isMobile || !showStatsPopup) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (
        target &&
        !target.closest("[data-stats-popup]") &&
        !target.closest("[data-status-trigger]")
      ) {
        setShowStatsPopup(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [isMobile, showStatsPopup]);

  useEffect(() => {
    const fetchBLVInfo = async () => {
      if (
        match.liveData &&
        match.liveData.length > 0 &&
        match.liveData[0].blv
      ) {
        setBlvLoading(true);
        try {
          const userInfo = await getUserInfo("admin", 1000);
          console.log("BLV info:", userInfo);
          setBlvInfo(userInfo);
        } catch (error) {
          console.error("Failed to fetch BLV info:", error);
        } finally {
          setBlvLoading(false);
        }
      }
    };

    fetchBLVInfo();

    // Cleanup function to clear timeout when component unmounts
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, [match.liveData]);

  if (
    !match ||
    !match.homeTeam ||
    !match.awayTeam ||
    !match.status ||
    typeof match.homeTeam.score !== "number" ||
    typeof match.awayTeam.score !== "number"
  ) {
    return <></>;
  }

  const leagueName = match.league || "Unknown League";
  const homeTeamName = match.homeTeam.name || "Home Team";
  const awayTeamName = match.awayTeam.name || "Away Team";
  const homeTeamLogo = match.homeTeam.logo || "/icon/bong-da.svg";
  const awayTeamLogo = match.awayTeam.logo || "/icon/bong-da.svg";

  // Handle click on match card
  const handleClick = () => {
    if (onClick) {
      onClick();
      return;
    }

    const homeTeamSlug = homeTeamName.toLowerCase().replace(/\s+/g, "-");
    const awayTeamSlug = awayTeamName.toLowerCase().replace(/\s+/g, "-");
    const matchSlug = `${homeTeamSlug}-vs-${awayTeamSlug}`;

    let videoUrl = "";
    if (match.liveData && match.liveData.length > 0 && match.liveData[0].hls) {
      videoUrl = match.liveData[0].hls;
    } else if (match.links && match.links.length > 0) {
      videoUrl = match.links[0];
    }

    if (videoUrl) {
      window.location.href = `/truc-tiep/${matchSlug}/streamer/${
        match.id
      }?video=${encodeURIComponent(videoUrl)}`;
    } else {
      window.location.href = `/truc-tiep/${matchSlug}/streamer/${match.id}`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "live":
        return "bg-red-500 text-white";
      case "ht":
      case "halftime":
        return "bg-green-500 text-white";
      case "pending":
      case "sắp diễn ra":
      case "cuối tuần":
        return "bg-yellow-500 text-white";
      default:
        return "bg-blue-500 text-white";
    }
  };

  const handleStatusHover = (e: React.MouseEvent) => {
    // On mobile, don't show on hover
    if (isMobile) return;

    setIsHoveringStatus(true);

    // Prevent multiple rapid calls and unnecessary re-renders
    if (showStatsPopup) return;

    // Capture the currentTarget immediately to avoid null reference issues
    const target = e.currentTarget;
    if (!target) return;

    // Use requestAnimationFrame to ensure smooth positioning
    requestAnimationFrame(() => {
      try {
        const rect = target.getBoundingClientRect();
        const position = {
          x: rect.left + rect.width / 2,
          y: rect.bottom + 10,
        };
        setPopupPosition(position);
        setShowStatsPopup(true);
      } catch (error) {
        console.error("Error getting bounding rect:", error);
        // Fallback: show popup without specific positioning
        setShowStatsPopup(true);
      }
    });
  };

  const handleStatusLeave = () => {
    // On mobile, don't handle mouse leave
    if (isMobile) return;

    setIsHoveringStatus(false);

    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    // Add a delay to prevent popup from disappearing too quickly
    hoverTimeoutRef.current = setTimeout(() => {
      if (!isHoveringPopup && !isHoveringStatus) {
        setShowStatsPopup(false);
      }
    }, 300);
  };

  const handleStatusClick = (e: React.MouseEvent) => {
    // Only handle click on mobile
    if (!isMobile) return;

    e.preventDefault();
    e.stopPropagation();

    if (showStatsPopup) {
      setShowStatsPopup(false);
      return;
    }

    // Capture the currentTarget immediately to avoid null reference issues
    const target = e.currentTarget;
    if (!target) return;

    // Calculate position relative to viewport
    const rect = target.getBoundingClientRect();
    const position = {
      x: rect.left + rect.width / 2,
      y: rect.bottom + 10,
    };
    setPopupPosition(position);
    setShowStatsPopup(true);
  };

  const renderCompetitionHeader = () => {
    if (variant === "compact") {
      return (
        <div className="flex items-center justify-between mb-2 lg:mb-3 relative z-10">
          <div className="flex items-center gap-1 sm:gap-2">
            <div className="w-2.5 h-2.5 sm:w-3 sm:h-3 bg-blue-500 rounded-sm"></div>
            <span className="text-xs text-gray-900 dark:text-white font-medium truncate max-w-[70px] sm:max-w-[90px] lg:max-w-[120px]">
              {leagueName}
            </span>
          </div>
          <div className="flex flex-col items-end gap-1">
            <div
              className="relative"
              onMouseEnter={handleStatusHover}
              onMouseLeave={handleStatusLeave}
              onClick={handleStatusClick}
              data-status-trigger
            >
              <span
                className={`inline-flex items-center px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs font-medium cursor-pointer hover:opacity-80 hover:scale-105 hover:shadow-lg transition-all duration-200 ${getStatusColor(
                  match.status
                )}`}
              >
                {getStatusText(match.status)}
              </span>
            </div>
            <div className="text-xs text-gray-600 dark:text-custom-subtle">
              {formatTime(match.time || "")}
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="relative mb-2 lg:mb-4">
        <div className="relative h-8 flex items-center justify-center">
          <svg
            width="131"
            height="29"
            viewBox="0 0 131 29"
            fill="none"
            className="absolute top-0 left-1/2 -translate-x-1/2"
          >
            <foreignObject x="-19.333" y="-20" width="170" height="69">
              <div
                style={{
                  backdropFilter: "blur(10px)",
                  clipPath: "url(#bgblur_clip_path)",
                  height: "100%",
                  width: "100%",
                }}
              />
            </foreignObject>

            <g>
              <path
                d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z"
                fill="url(#paint0_linear)"
              />
              <path
                d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z"
                stroke="url(#paint1_linear)"
              />
              <path
                d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z"
                stroke="url(#paint2_linear)"
                strokeOpacity="0.2"
              />
            </g>

            {/* Definitions */}
            <defs>
              <clipPath id="bgblur_clip_path" transform="translate(19.333 20)">
                <path d="M126.056 0.5C123.603 1.73657 121.748 4.02604 121.13 6.83594L121.048 7.25195L118.564 21.5771C117.871 25.5782 114.399 28.4998 110.338 28.5H22.1123C18.5501 28.5 15.3942 26.2423 14.2295 22.9023L14.123 22.5752L13.1484 19.3662L10.1172 6.67676C9.46046 3.92808 7.62737 1.70909 5.22949 0.5H126.056Z" />
              </clipPath>

              {/* Gradients */}
              <linearGradient
                id="paint0_linear"
                x1="65.667"
                y1="29"
                x2="65.667"
                y2="0"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#00D962" />
                <stop offset="1" stopColor="#007334" />
              </linearGradient>
              <linearGradient
                id="paint1_linear"
                x1="65.667"
                y1="29"
                x2="65.667"
                y2="0"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#0A6027" />
                <stop offset="1" stopColor="#666666" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint2_linear"
                x1="15.167"
                y1="25"
                x2="117.167"
                y2="25.5"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="white" />
                <stop offset="1" stopColor="white" />
              </linearGradient>
            </defs>
          </svg>
          {/* Status text inside notch */}
          {isMobile ? (
            // Mobile: Use click without Tooltip wrapper
            <div
              className="absolute top-1/2 left-1/2 max-w-[80%] -translate-x-1/2 -translate-y-1/2 z-10"
              onClick={handleStatusClick}
              data-status-trigger
            >
              <span className="inline-block truncate text-xs whitespace-nowrap text-white lg:text-sm cursor-pointer hover:opacity-80 hover:scale-110 hover:shadow-lg transition-all duration-200">
                {getStatusText(match.status)}
              </span>
            </div>
          ) : (
            // Desktop: Use Tooltip with hover
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="absolute top-1/2 left-1/2 max-w-[80%] -translate-x-1/2 -translate-y-1/2 z-10"
                  onMouseEnter={handleStatusHover}
                  onMouseLeave={handleStatusLeave}
                  data-status-trigger
                >
                  <span className="inline-block truncate text-xs whitespace-nowrap text-white lg:text-sm cursor-pointer hover:opacity-80 hover:scale-110 hover:shadow-lg transition-all duration-200">
                    {getStatusText(match.status)}
                  </span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <div className="py-2 px-4" data-stats-popup>
                  <MatchTooltip
                    matchStats={match.statistics ? [match.statistics] : []}
                    homeTeamName={homeTeamName}
                    awayTeamName={awayTeamName}
                    homeTeamId={match.id || ""}
                    awayTeamId={match.id || ""}
                    leagueName={leagueName}
                    matchStatus={match.status}
                  />
                </div>
              </TooltipContent>
            </Tooltip>
          )}{" "}
          {/* Left side - Competition */}
          <div className="absolute left-0 top-0 flex items-center gap-1 rounded-full border border-[#FF6601] px-1 py-0.5 bg-white">
            <p className="w-[80px] truncate text-xs font-normal uppercase xl:w-[120px] text-gray-900 px-1">
              {leagueName}
            </p>
          </div>
          {/* Right side - Time */}
          <div className="absolute right-0 top-0">
            <span className="rounded-full border border-[#FF6601] bg-gradient-to-r from-[#F03131] to-[#FF6601] bg-clip-text px-2 py-1 text-xs font-semibold text-transparent lg:text-sm">
              {formatTime(match.time || "")}
            </span>
          </div>
        </div>
      </div>
    );
  };

  const renderTeamsAndScore = () => {
    if (variant === "compact") {
      return (
        <div className="flex items-center justify-between mb-2 lg:mb-3 relative z-10">
          {/* Home Team */}
          <div className="flex items-center gap-1 sm:gap-2 flex-1 min-w-0">
            <span className="text-xs sm:text-sm text-gray-900 dark:text-white font-medium truncate text-left w-[80px] sm:w-[100px] lg:w-[120px]">
              {homeTeamName}
            </span>
            <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-custom-dark-secondary flex-shrink-0">
              <img
                src={homeTeamLogo}
                alt={homeTeamName}
                className="w-full h-full object-cover p-1"
              />
            </div>
          </div>

          {/* Score Display */}
          <div className="flex items-center gap-1 mx-1 lg:mx-2 flex-shrink-0">
            {match.homeTeam.score === 0 && match.awayTeam.score === 0 ? (
              <>
                <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                  0
                </div>
                <span className="text-gray-400 dark:text-custom-subtle text-sm sm:text-base lg:text-lg font-bold">
                  :
                </span>
                <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                  0
                </div>
              </>
            ) : (
              <>
                <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                  {match.homeTeam.score}
                </div>
                <span className="text-gray-400 dark:text-custom-subtle text-sm sm:text-base lg:text-lg font-bold">
                  :
                </span>
                <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                  {match.awayTeam.score}
                </div>
              </>
            )}
          </div>

          {/* Away Team */}
          <div className="flex items-center gap-1 sm:gap-2 flex-1 justify-end min-w-0">
            <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-custom-dark-secondary flex-shrink-0">
              <img
                src={awayTeamLogo}
                alt={awayTeamName}
                className="w-full h-full object-cover p-1"
              />
            </div>
            <span className="text-xs sm:text-sm text-gray-900 dark:text-white font-medium truncate text-right w-[80px] sm:w-[100px] lg:w-[120px]">
              {awayTeamName}
            </span>
          </div>
        </div>
      );
    }

    // Detailed variant
    return (
      <div className="flex items-center justify-evenly gap-6 mb-2 lg:mb-3">
        {/* Home Team */}
        <div className="flex items-center gap-3 min-w-0">
          <span className="font-medium text-gray-900 dark:text-white truncate w-[120px] sm:w-[150px] lg:w-[200px]">
            {homeTeamName}
          </span>
          <div className="w-10 h-10 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-gray-600 flex-shrink-0">
            <img
              src={homeTeamLogo}
              alt={homeTeamName}
              className="w-full h-full object-cover p-1"
            />
          </div>
        </div>

        <div className="rounded-full border border-[#2563eb]/60 bg-[#2563eb]/60">
          {/* Score */}
          <div className="text-2xl font-bold text-gray-900 dark:text-white flex-shrink-0 min-w-[80px] text-center mx-4">
            <span className="">
              {match.homeTeam.score} - {match.awayTeam.score}
            </span>
          </div>
        </div>

        {/* Away Team */}
        <div className="flex items-center gap-3 min-w-0">
          <div className="w-10 h-10 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-gray-600 flex-shrink-0">
            <img
              src={awayTeamLogo}
              alt={awayTeamName}
              className="w-full h-full object-cover p-1"
            />
          </div>
          <span className="font-medium text-gray-900 dark:text-white truncate w-[120px] sm:w-[150px] lg:w-[200px]">
            {awayTeamName}
          </span>
        </div>
      </div>
    );
  };
  const renderMatchStatus = () => {
    return (
      <div className="flex md:justify-evenly items-center dark:bg-white justify-center gap-2 [&_div]:px-1 my-4 lg:mb-3 relative z-10 border border-gray-700 border-opacity-25 rounded-3xl px-2 py-1 lg:mx-[100px] sm:mx-[80px] mx-16 dark:divide-x-2 divide-yellow-600 divide-x-2">
        <div className="flex gap-2">
          <p className="p-1 text-green-500 text-xs font-semibold">HT</p>
          <span className="dark:text-black">0 - 0</span>
        </div>
        <div className="flex gap-2">
          <Image src="/icon/corner.svg" alt="yellow" width={18} height={18} />
          <span className="dark:text-black">0 - 0</span>
        </div>{" "}
        <div className="flex gap-2">
          <Image src="/icon/yellow.svg" alt="yellow" width={18} height={18} />
          <span className="dark:text-black">0 - 0</span>
        </div>
      </div>
    );
  };

  const renderBLVSection = () => {
    // Get BLV ID from liveData
    const blvId = match.liveData[0]?.blv;
    console.log("BLV ID:", blvId);
    console.log("demo", match.liveData);

    if (!blvId) {
      return null; // Don't render BLV section if no BLV data
    }

    // Get display name
    const displayName = blvInfo?.displayName;

    const displayPhotoURL = blvInfo?.photoURL || "B";

    return (
      <div className="flex flex-col items-center gap-1 lg:gap-1.5 mb-2 lg:mb-3 relative z-10">
        {/* Avatar */}
        <div className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 rounded-full overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex-shrink-0">
          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
            {blvLoading ? (
              <div className="animate-spin h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6"></div>
            ) : (
              <span className="text-white text-sm sm:text-base lg:text-lg font-bold">
                {displayPhotoURL}
              </span>
            )}
          </div>
        </div>

        {/* BLV Name */}
        <span className="text-xs lg:text-sm text-gray-900 dark:text-white font-medium text-center">
          {blvLoading ? "Đang tải..." : displayName}
        </span>
      </div>
    );
  };

  const renderStats = () => {
    if (variant === "detailed") {
      return (
        <div className="grid grid-cols-10 relative z-10 mt-auto">
          <div className="col-span-7 overflow-x-hidden">
            {renderBLVSection()}
          </div>
          <div className="col-span-3 gap-1 sm:gap-1.5">
            {/* Vendor Logo */}
            <div className="w-12 h-10 sm:w-16 sm:h-12 rounded flex items-center justify-center">
              <img
                src="/vendor/ok-logo.png"
                alt="OK Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      );
    }

    // Compact variant
    return (
      <div className="flex items-center justify-between relative z-10">
        <div className="flex items-center gap-1.5 lg:gap-2">
          {/* Cards Display */}
          <div className="flex items-center gap-2">
            {/* Home Team Cards */}
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-600 dark:text-gray-400">
                H:
              </span>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.redHome || 0 }).map(
                  (_, index) => (
                    <div
                      key={`red-home-${index}`}
                      className="w-4 h-4 bg-red-500 rounded-sm border-2 border-red-700 shadow-md"
                    ></div>
                  )
                )}
              </div>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.yellowHome || 0 }).map(
                  (_, index) => (
                    <div
                      key={`yellow-home-${index}`}
                      className="w-4 h-4 bg-yellow-400 rounded-sm border-2 border-yellow-600 shadow-md"
                    ></div>
                  )
                )}
              </div>
            </div>

            {/* Away Team Cards */}
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-600 dark:text-gray-400">
                K:
              </span>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.yellowAway || 0 }).map(
                  (_, index) => (
                    <div
                      key={`yellow-away-${index}`}
                      className="w-4 h-4 bg-yellow-400 rounded-sm border-2 border-yellow-600 shadow-md"
                    ></div>
                  )
                )}
              </div>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.redAway || 0 }).map(
                  (_, index) => (
                    <div
                      key={`red-away-${index}`}
                      className="w-4 h-4 bg-red-500 rounded-sm border-2 border-red-700 shadow-md"
                    ></div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-1.5 lg:gap-2">
          {/* Vendor Logo */}
          <div className="w-12 h-10 sm:w-16 sm:h-12 rounded flex items-center justify-center">
            <img
              src="/vendor/ok-logo.png"
              alt="OK Logo"
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      </div>
    );
  };

  const cardContent = (
    <div
      className={`relative ${
        variant === "compact" ? "p-2 sm:p-2 lg:p-3" : "p-2 lg:p-4"
      }`}
    >
      {/* Competition Header */}
      {renderCompetitionHeader()}

      {/* Teams and Score */}
      {renderTeamsAndScore()}

      {/* Divider */}
      <div className="flex justify-center mb-2 lg:mb-3 relative z-10">
        <div className="w-1/3 h-px bg-gradient-to-r from-transparent via-blue-500 to-transparent"></div>
      </div>

      {/* Match Status */}
      {renderMatchStatus()}

      {/* BLV Section */}
      {/* {renderBLVSection()} */}

      {/* Statistics */}
      {renderStats()}
    </div>
  );

  // If no href provided, create default navigation URL
  const flvValue =
    typeof match.flv === "string" ? match.flv : match.flv?.url || "a";
  const defaultHref = `/truc-tiep/${match.id || "match"}/${flvValue}/${
    match.id || "id"
  }`;

  return (
    <>
      <div
        className={`relative rounded-lg border border-gray-200 dark:border-custom-dark-secondary bg-white dark:bg-custom-dark overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 cursor-pointer ${className}`}
        style={{
          ...style,
          backgroundImage: "url(/bg/bg-card.jpg)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        // onClick={handleClick}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/60 to-white/40 dark:from-custom-dark/70 dark:to-custom-dark/60 z-0"></div>

        {cardContent}
      </div>

      {/* Mobile Stats Popup */}
      {isMobile && showStatsPopup && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          data-stats-popup
        >
          <div className="bg-white dark:bg-custom-dark rounded-lg max-w-md w-[85vw] max-h-[80vh] overflow-y-auto">
            <div className="p-4 mx-2">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Thống kê trận đấu</h3>
                <button
                  type="button"
                  onClick={() => setShowStatsPopup(false)}
                  className="text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  X
                </button>
              </div>
              <MatchTooltip
                matchStats={match.statistics ? [match.statistics] : []}
                homeTeamName={homeTeamName}
                awayTeamName={awayTeamName}
                homeTeamId={match.id || ""}
                awayTeamId={match.id || ""}
                leagueName={leagueName}
                matchStatus={match.status}
              />
            </div>
          </div>
        </div>
      )}

      {/* Stats Popup */}
      {/* <MatchStatsPopup
        isVisible={showStatsPopup}
        onClose={() => setShowStatsPopup(false)}
        matchStats={match.statistics ? [match.statistics] : []}
        homeTeamName={homeTeamName}
        awayTeamName={awayTeamName}
        homeTeamId={match.id || ""}
        awayTeamId={match.id || ""}
        leagueName={leagueName}
        matchStatus={match.status}
        position={popupPosition || undefined}
        onMouseEnter={() => {
          setIsHoveringPopup(true);
          // Clear timeout when entering popup
          if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
          }
        }}
        onMouseLeave={() => {
          setIsHoveringPopup(false);
          // Start timeout when leaving popup
          if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
          }
          hoverTimeoutRef.current = setTimeout(() => {
            if (!isHoveringStatus) {
              setShowStatsPopup(false);
            }
          }, 200);
        }}
      /> */}
    </>
  );
}
