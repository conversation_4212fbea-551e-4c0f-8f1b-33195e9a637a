"use client";

interface LineupTabProps {
  isLoading?: boolean;
}

export default function LineupTab({ isLoading = false }: LineupTabProps) {
  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="h-full space-y-6 p-6">
      {/* Manchester United Formation Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="text-center py-8">
          <div className="space-y-3">
            <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Liverpool Formation Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="text-center py-8">
          <div className="space-y-3">
            <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Substitutes Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="text-center py-8">
          <div className="space-y-3">
            <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );

  // Show skeleton when loading
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="h-full space-y-6 p-6">
      {/* Manchester United Formation */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-blue-600 mb-4">Manchester United</h3>
        
        <div className="text-center py-8">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
        

      </div>

      {/* Liverpool Formation */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-red-600 mb-4">Liverpool</h3>
        
        <div className="text-center py-8">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
        

      </div>

      {/* Substitutes */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Dự bị</h3>
        <div className="text-center py-8">
          <div className="text-gray-500 dark:text-gray-400 text-sm">
            Thông tin chi tiết sẽ được cập nhật khi trận đấu bắt đầu
          </div>
        </div>
      </div>
    </div>
  );
} 