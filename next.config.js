/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      { protocol: 'https', hostname: 'ext.same-assets.com' },
      { protocol: 'https', hostname: 'api-ck.686868.me' },
      { protocol: 'https', hostname: 'images.dmca.com' },
      { protocol: 'https', hostname: 'images.unsplash.com' },
      { protocol: 'https', hostname: 'upload.wikimedia.org' },
      { protocol: 'https', hostname: 'g7w1r.4shares.live' },
    ],
  },
  async rewrites() {
    return [
      {
        source: '/api/proxy/:path*',
        destination: 'https://www.ngoaihangtv.xyz/api/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
