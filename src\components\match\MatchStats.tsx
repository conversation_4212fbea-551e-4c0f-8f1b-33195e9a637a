"use client";

import MatchBLVSection from "./MatchBLVSection";
import { MatchData } from "@/types/match";
import { UserInfo } from "@/services/userService";

interface MatchStatsProps {
  variant?: "default" | "compact" | "detailed";
  match: MatchData;
  blvInfo: UserInfo | null;
  blvLoading: boolean;
}

export default function MatchStats({
  variant = "default",
  match,
  blvInfo,
  blvLoading,
}: MatchStatsProps) {
  if (variant === "detailed") {
    return (
      <div className="grid grid-cols-10 relative z-10 mt-auto">
        <div className="col-span-7 overflow-x-hidden">
          <MatchBLVSection
            blvId={match.liveData?.[0]?.blv}
            blvInfo={blvInfo}
            blvLoading={blvLoading}
          />
        </div>
        <div className="col-span-3 gap-1 sm:gap-1.5">
          {/* Vendor Logo */}
          <div className="w-12 h-10 sm:w-16 sm:h-12 rounded flex items-center justify-center">
            <img
              src="/vendor/ok-logo.png"
              alt="OK Logo"
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      </div>
    );
  }

  // Compact variant
  return (
    <div className="flex items-center justify-between relative z-10">
      <div className="flex items-center gap-1.5 lg:gap-2">
        {/* Cards Display */}
        <div className="flex items-center gap-2">
          {/* Home Team Cards */}
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-600 dark:text-gray-400">H:</span>
            <div className="flex gap-1">
              {Array.from({ length: match.cards?.redHome || 0 }).map(
                (_, index) => (
                  <div
                    key={`red-home-${index}`}
                    className="w-4 h-4 bg-red-500 rounded-sm border-2 border-red-700 shadow-md"
                  ></div>
                )
              )}
            </div>
            <div className="flex gap-1">
              {Array.from({ length: match.cards?.yellowHome || 0 }).map(
                (_, index) => (
                  <div
                    key={`yellow-home-${index}`}
                    className="w-4 h-4 bg-yellow-400 rounded-sm border-2 border-yellow-600 shadow-md"
                  ></div>
                )
              )}
            </div>
          </div>

          {/* Away Team Cards */}
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-600 dark:text-gray-400">K:</span>
            <div className="flex gap-1">
              {Array.from({ length: match.cards?.yellowAway || 0 }).map(
                (_, index) => (
                  <div
                    key={`yellow-away-${index}`}
                    className="w-4 h-4 bg-yellow-400 rounded-sm border-2 border-yellow-600 shadow-md"
                  ></div>
                )
              )}
            </div>
            <div className="flex gap-1">
              {Array.from({ length: match.cards?.redAway || 0 }).map(
                (_, index) => (
                  <div
                    key={`red-away-${index}`}
                    className="w-4 h-4 bg-red-500 rounded-sm border-2 border-red-700 shadow-md"
                  ></div>
                )
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-1.5 lg:gap-2">
        {/* Vendor Logo */}
        <div className="w-12 h-10 sm:w-16 sm:h-12 rounded flex items-center justify-center">
          <img
            src="/vendor/ok-logo.png"
            alt="OK Logo"
            className="w-full h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}
