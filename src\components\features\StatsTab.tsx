"use client";

interface StatsTabProps {
  isLoading?: boolean;
}

export default function StatsTab({ isLoading = false }: StatsTabProps) {
  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="h-full space-y-2 p-2">
      {/* Match Info Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="grid grid-cols-2 gap-3 text-sm">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="space-y-1">
              <div className="w-20 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="w-32 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Score Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="flex items-center justify-center gap-6 mb-2">
          <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="w-12 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Stats Grid Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-2 animate-pulse"></div>
        <div className="space-y-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="w-24 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex items-center gap-2">
                <div className="w-8 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div className="w-8 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Show skeleton when loading
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="h-full space-y-2 p-2">
      {/* Match Info */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Thông tin trận đấu</h3>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <span className="text-gray-600 dark:text-gray-400">Giải đấu:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">Premier League 2024/25</span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Vòng:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">Vòng 15</span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Sân vận động:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">Old Trafford</span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">Trọng tài:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">Michael Oliver</span>
          </div>
        </div>
      </div>

      {/* Score */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Tỷ số</h3>
        <div className="flex items-center justify-center gap-6 mb-2">
          <span className="text-lg font-semibold text-gray-900 dark:text-white">Manchester United</span>
          <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">2</span>
          <span className="text-lg font-semibold text-gray-900 dark:text-white">Liverpool</span>
        </div>
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          Hiệp 1: 1-1 | Hiệp 2: 1-0
        </div>
      </div>

      {/* Stats Grid */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Thống kê</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Tỷ lệ kiểm soát bóng</span>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 dark:text-white">45%</span>
              <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div className="w-[45%] h-full bg-blue-500 rounded-full"></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">55%</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Phạt góc</span>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 dark:text-white">4</span>
              <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div className="w-[40%] h-full bg-blue-500 rounded-full"></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">6</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Bàn thắng</span>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 dark:text-white">2</span>
              <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div className="w-[67%] h-full bg-blue-500 rounded-full"></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">1</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Thẻ</span>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 dark:text-white">2</span>
              <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div className="w-[50%] h-full bg-blue-500 rounded-full"></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">2</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Sút bóng</span>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 dark:text-white">8</span>
              <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div className="w-[53%] h-full bg-blue-500 rounded-full"></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">7</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Tấn công</span>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-900 dark:text-white">12</span>
              <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                <div className="w-[60%] h-full bg-blue-500 rounded-full"></div>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">8</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}