"use client";

import { useState, useEffect } from "react";
import { MatchData } from '@/types/match';
import { fetchMatches } from '@/services/matchService';
import MatchCard from '@/components/common/MatchCard';

interface RelatedMatchesTabProps {
  currentMatchId?: string;
  currentCategory?: string;
}

export default function RelatedMatchesTab({ currentMatchId, currentCategory }: RelatedMatchesTabProps) {
  const [relatedMatches, setRelatedMatches] = useState<MatchData[]>([]);
  const [upcomingMatches, setUpcomingMatches] = useState<MatchData[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingUpcoming, setLoadingUpcoming] = useState(true);

  // Fetch related matches (hot matches excluding current live match)
  useEffect(() => {
    const fetchRelatedMatches = async () => {
      if (!currentCategory) return;

      try {
        setLoading(true);
        const result = await fetchMatches({
          category: currentCategory,
          typeMatch: 'hot',
          limit: 10,
          offset: 0,
          sortBy: 'status,time,date',
          sortOrder: 'DESC,ASC,ASC'
        });

        // Filter out current live match and only show non-live matches
        const filteredMatches = result.data.filter(match =>
          match.id !== currentMatchId && match.status !== 'live'
        );

        setRelatedMatches(filteredMatches.slice(0, 6)); // Limit to 6 matches
      } catch (error) {
        console.error('Error fetching related matches:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRelatedMatches();
  }, [currentCategory, currentMatchId]);

  // Fetch upcoming matches
    useEffect(() => {
    const fetchUpcomingMatches = async () => {
      if (!currentCategory) return;

      try {
        setLoadingUpcoming(true);
        const result = await fetchMatches({
          category: currentCategory,
          status: 'pending',
          limit: 10,
          offset: 0,
          sortBy: 'time,date',
          sortOrder: 'ASC,ASC'
        });

        setUpcomingMatches(result.data.slice(0, 6)); // Limit to 6 matches
      } catch (error) {
        console.error('Error fetching upcoming matches:', error);
      } finally {
        setLoadingUpcoming(false);
      }
    };

    fetchUpcomingMatches();
  }, [currentCategory]);

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 lg:gap-4">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="group relative overflow-hidden rounded-xl bg-white dark:bg-custom-dark min-h-[160px] lg:min-h-[180px] animate-pulse">
          <div className="relative p-2 lg:p-4">
            {/* Competition Header Skeleton */}
            <div className="relative mb-2 lg:mb-4">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            </div>

            {/* Teams Skeleton */}
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>

            {/* Time/Status Skeleton */}
            <div className="mt-3 h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      ))}
      </div>
    );

  return (
    <div className="h-full space-y-2 lg:space-y-4">
      {/* Related Matches Header */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2 lg:p-6 mx-2 lg:mx-0">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-custom-light mb-2 lg:mb-4">
          Các trận đấu liên quan
        </h3>

        {loading ? (
          <LoadingSkeleton />
        ) : relatedMatches.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 lg:gap-4">
          {relatedMatches.map((match, i) => (
              <MatchCard
                key={match.id || i}
                match={match}
                variant="detailed"
                className="hover:border-blue-500 hover:shadow-lg transition-all duration-300"
              />
                    ))}
                  </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            Không có trận đấu liên quan
                    </div>
        )}
      </div>

      {/* Upcoming Matches Section */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-2 lg:p-6 mx-2 lg:mx-0">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-custom-light mb-2 lg:mb-4">
          Trận đấu sắp diễn ra
        </h3>

        {loadingUpcoming ? (
          <LoadingSkeleton />
        ) : upcomingMatches.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 lg:gap-4">
            {upcomingMatches.map((match, i) => (
              <MatchCard
                key={match.id || i}
                match={match}
                variant="detailed"
                className="hover:border-blue-500 hover:shadow-lg transition-all duration-300"
              />
                    ))}
                  </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            Không có trận đấu sắp diễn ra
                    </div>
        )}
      </div>
    </div>
  );
} 