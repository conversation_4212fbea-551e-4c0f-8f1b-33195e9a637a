"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

import { StatisticsData } from "@/types/match";
import { useState } from "react";

type MatchStats = StatisticsData;

interface MatchStatsPopupProps {
  matchStats: MatchStats[];
  homeTeamName: string;
  awayTeamName: string;
  homeTeamId: string;
  awayTeamId: string;
  leagueName: string;
  matchStatus: string;
}

export default function MatchTooltip({
  matchStats,
  homeTeamName,
  awayTeamName,
  homeTeamId,
  awayTeamId,
  leagueName,
  matchStatus,
}: MatchStatsPopupProps) {
  const [activeTab, setActiveTab] = useState("first");

  if (!matchStats || matchStats.length === 0) return null;

  // Map tab to matchStats index
  const getStatsIndex = (tab: string) => {
    switch (tab) {
      case "first":
        return 1;
      case "second":
        return 2;
      case "full":
        return 0;
      default:
        return 0;
    }
  };

  const statsIndex = getStatsIndex(activeTab);
  const stats = matchStats[statsIndex] || matchStats[0];

  if (!stats) {
    return (
      <div className="flex items-center justify-center">
        <div
          className="rounded-lg p-4 w-full max-w-md"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="text-white text-center">
            <h3 className="font-medium mb-2">{leagueName}</h3>
            <p className="text-sm font-semibold text-gray-200 mb-4">
              {homeTeamName} vs {awayTeamName}
            </p>
            <p className="text-xs text-gray-400">
              Không có dữ liệu thống kê chi tiết
            </p>
            <p className="text-xs text-gray-400 mt-2">Status: {matchStatus}</p>
          </div>
        </div>
      </div>
    );
  }

  const getStatValue = (value: number, isPercentage = false) =>
    isPercentage ? `${value}%` : value.toString();

  const getStatBarWidth = (
    homeValue: number,
    awayValue: number,
    isPercentage = false
  ) => {
    const total = homeValue + awayValue;
    return total > 0 ? (homeValue / total) * 100 : 50;
  };

  const statsData = [
    {
      label: "Tỷ lệ kiểm soát bóng",
      homeValue: stats.possession?.home || 0,
      awayValue: stats.possession?.away || 0,
      isPercentage: true,
    },
    {
      label: "Phạt góc",
      homeValue: stats.corners?.home || 0,
      awayValue: stats.corners?.away || 0,
    },
    {
      label: "Bàn thắng",
      homeValue: stats.goals?.home || 0,
      awayValue: stats.goals?.away || 0,
    },
    {
      label: "Thẻ",
      homeValue: stats.cards?.home || 0,
      awayValue: stats.cards?.away || 0,
    },
    {
      label: "Sút bóng",
      homeValue: stats.shots?.home || 0,
      awayValue: stats.shots?.away || 0,
    },
    {
      label: "Tấn công",
      homeValue: stats.attacks?.home || 0,
      awayValue: stats.attacks?.away || 0,
    },
    {
      label: "Chuyền bóng",
      homeValue: stats.passes?.home || 0,
      awayValue: stats.passes?.away || 0,
    },
  ];

  const RenderStats = () => (
    <div className="z-50 flex items-center justify-center w-full">
      <div
        className="py-1 w-full max-w-md"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white font-medium text-sm">{leagueName}</h3>
          <div className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
            {matchStatus === "live" ? "LIVE" : matchStatus.toUpperCase()}
          </div>
        </div>

        {/* Match Info */}
        <div className="text-center mb-4">
          <p className="text-sm font-semibold text-gray-200">
            {homeTeamName} vs {awayTeamName}
          </p>
        </div>

        {/* Statistics */}
        <div className="space-y-3 max-h-64 overflow-y-auto scrollbar-hide">
          {statsData.map((stat, index) => (
            <div key={index} className="space-y-2">
              {/* Values Row */}
              <div className="flex items-center justify-between text-xs">
                <span className="text-white font-medium w-16 text-right">
                  {getStatValue(stat.homeValue, stat.isPercentage)}
                </span>
                <span className="text-gray-300 text-center flex-1 px-2">
                  {stat.label}
                </span>
                <span className="text-white font-medium w-16 text-left">
                  {getStatValue(stat.awayValue, stat.isPercentage)}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${getStatBarWidth(
                      stat.homeValue,
                      stat.awayValue,
                      stat.isPercentage
                    )}%`,
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Config tab
  const tabConfig = [
    { value: "first", label: "Hiệp 1" },
    { value: "second", label: "Hiệp 2" },
    { value: "full", label: "Cả trận" },
  ];

  return (
    <Tabs
      defaultValue="first"
      className="w-full"
      onValueChange={(val) => setActiveTab(val)}
    >
      <TabsList className="flex justify-evenly w-full">
        {tabConfig.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value}>
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabConfig.map((tab) => (
        <TabsContent key={tab.value} value={tab.value}>
          <RenderStats />
        </TabsContent>
      ))}
    </Tabs>
  );
}
